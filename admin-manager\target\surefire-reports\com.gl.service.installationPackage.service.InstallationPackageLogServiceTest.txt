-------------------------------------------------------------------------------
Test set: com.gl.service.installationPackage.service.InstallationPackageLogServiceTest
-------------------------------------------------------------------------------
Tests run: 15, Failures: 5, Errors: 0, Skipped: 0, Time elapsed: 2.099 s <<< FAILURE! - in com.gl.service.installationPackage.service.InstallationPackageLogServiceTest
testList_WithOnlySearchCondition_ShouldReturnValidResult  Time elapsed: 1.969 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: 总数应该匹配 ==> expected: <1> but was: <0>
	at com.gl.service.installationPackage.service.InstallationPackageLogServiceTest.testList_WithOnlySearchCondition_ShouldReturnValidResult(InstallationPackageLogServiceTest.java:207)

testList_WithExportTypeNotOne_ShouldNotAddPagination  Time elapsed: 0 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: 总数应该匹配 ==> expected: <2> but was: <0>
	at com.gl.service.installationPackage.service.InstallationPackageLogServiceTest.testList_WithExportTypeNotOne_ShouldNotAddPagination(InstallationPackageLogServiceTest.java:180)

testList_WithNullDto_ShouldReturnValidResult  Time elapsed: 0.017 s  <<< FAILURE!
java.lang.AssertionError
	at com.gl.service.installationPackage.service.InstallationPackageLogServiceTest.testList_WithNullDto_ShouldReturnValidResult(InstallationPackageLogServiceTest.java:152)

testList_WithSearchConditionAndTimeRange_ShouldReturnSuccessResult  Time elapsed: 0 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: 总数应该匹配 ==> expected: <2> but was: <0>
	at com.gl.service.installationPackage.service.InstallationPackageLogServiceTest.testList_WithSearchConditionAndTimeRange_ShouldReturnSuccessResult(InstallationPackageLogServiceTest.java:110)

testList_WithNoResults_ShouldReturnEmptyResult  Time elapsed: 0.026 s  <<< FAILURE!
org.mockito.exceptions.verification.opentest4j.ArgumentsAreDifferent: 

Argument(s) are different! Wanted:
jdbcTemplate.queryForObject(
    <any string>,
    class java.lang.Long,
    <any java.lang.Object[]>
);
-> at org.springframework.jdbc.core.JdbcTemplate.queryForObject(JdbcTemplate.java:906)
Actual invocations have different arguments:
jdbcTemplate.queryForObject(
    "select count(1) from (select 
l.id,
s.shop_name ,
d.`name` deviceName,
d.sn,
p.version_name ,
l.create_time ,
l.response_time,
l.status 
from dub_installation_package_log l 
left join dub_installation_package p on l.package_id = p.id 
left join dub_device d on l.device_id = d.id 
left join dub_shop s on s.id=d.shop_id where 1=1  and ( p.version_name like ?  or d.sn like ? )  and l.create_time >= ?  and l.create_time <= ? ) t",
    class java.lang.Long,
    "%test%",
    "%test%",
    "2024-01-01 00:00:00",
    "2024-12-31 23:59:59"
);
-> at com.gl.service.installationPackage.service.InstallationPackageLogService.list(InstallationPackageLogService.java:77)

	at com.gl.service.installationPackage.service.InstallationPackageLogServiceTest.testList_WithNoResults_ShouldReturnEmptyResult(InstallationPackageLogServiceTest.java:137)

